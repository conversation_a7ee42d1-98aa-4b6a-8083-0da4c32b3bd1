import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ILlmService,
  LlmRecommendation,
  CandidateEntity,
  ChatResponse,
  ConversationContext,
  UserIntent,
} from '../interfaces/llm.service.interface';

@Injectable()
export class GoogleGeminiLlmService implements ILlmService {
  private readonly logger = new Logger(GoogleGeminiLlmService.name);
  private readonly apiKey: string | undefined;
  private readonly apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('GOOGLE_GEMINI_API_KEY');
    if (!this.apiKey) {
      this.logger.warn('GOOGLE_GEMINI_API_KEY is not set in environment variables.');
    }
  }

  async getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation> {
    this.logger.log(
      `Getting Google Gemini recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`,
    );

    if (!this.apiKey) {
      this.logger.warn('Google Gemini API key not available, using fallback');
      return this.getFallbackRecommendation(candidateEntities);
    }

    try {
      const prompt = this.buildRecommendationPrompt(
        problemDescription,
        candidateEntities,
      );

      const response = await this.callGeminiAPI(prompt);
      const recommendation = this.parseGeminiResponse(response, candidateEntities);

      this.logger.log(
        `Google Gemini recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`,
      );

      return recommendation;
    } catch (error) {
      this.logger.error('Error generating Google Gemini recommendation', error.stack);
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private async callGeminiAPI(prompt: string): Promise<string> {
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1000,
      },
    };

    const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Google Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
      return data.candidates[0].content.parts[0].text;
    }

    throw new Error('Invalid response format from Google Gemini API');
  }

  private buildRecommendationPrompt(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): string {
    const entitiesContext = candidateEntities
      .map((entity, index) => {
        const categories = entity.categories
          .map((c) => c.category.name)
          .join(', ');
        const tags = entity.tags.map((t) => t.tag.name).join(', ');
        const features = entity.features.map((f) => f.feature.name).join(', ');

        return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType.name}
   - Description: ${entity.shortDescription || entity.description || 'No description available'}
   - Categories: ${categories || 'None'}
   - Tags: ${tags || 'None'}
   - Features: ${features || 'None'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'No ratings'}`;
      })
      .join('\n\n');

    return `You are an AI assistant helping users find the best AI tools and resources for their specific needs.

**User's Problem:**
"${problemDescription}"

**Available Options:**
${entitiesContext}

**Instructions:**
1. Analyze the user's problem and requirements
2. Recommend the TOP 3-5 most relevant options from the list above
3. Provide a clear explanation of why each recommendation fits the user's needs
4. Consider factors like: relevance to the problem, tool capabilities, user ratings, ease of use, and cost

**Response Format:**
Please respond in the following JSON format:
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [problem summary], I recommend: 1) [Tool Name] because [specific reason]... 2) [Tool Name] because [specific reason]... 3) [Tool Name] because [specific reason]..."
}

**Important:** Only include entity IDs that exist in the provided list above. Limit to maximum 5 recommendations.`;
  }

  private parseGeminiResponse(
    response: string,
    candidateEntities: CandidateEntity[],
  ): LlmRecommendation {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
        throw new Error('Invalid response structure');
      }

      // Filter to only include valid entity IDs
      const validEntityIds = candidateEntities.map((e) => e.id);
      const filteredIds = parsed.recommendedEntityIds.filter((id: string) =>
        validEntityIds.includes(id),
      );

      return {
        recommendedEntityIds: filteredIds.slice(0, 5), // Limit to 5
        explanation: parsed.explanation || 'AI recommendation generated successfully.',
      };
    } catch (error) {
      this.logger.warn('Failed to parse Google Gemini response, using fallback', error.message);
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private getFallbackRecommendation(candidateEntities: CandidateEntity[]): LlmRecommendation {
    // Simple fallback: return top 3 entities by rating, or first 3 if no ratings
    const sortedEntities = candidateEntities
      .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
      .slice(0, 3);

    return {
      recommendedEntityIds: sortedEntities.map((e) => e.id),
      explanation:
        'Based on the available options, here are the top-rated tools that might help with your needs. Please review each option to see which best fits your specific requirements.',
    };
  }

  // Chat-specific methods implementation
  async getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse> {
    const startTime = Date.now();

    this.logger.log(
      `Getting Google Gemini chat response for session: ${context.sessionId}, stage: ${context.conversationStage}`,
    );

    if (!this.apiKey) {
      this.logger.warn('Google Gemini API key not available, using fallback');
      return this.getFallbackChatResponse(userMessage, context);
    }

    try {
      // First classify the intent
      const intent = await this.classifyIntent(userMessage, context);

      // Build the chat prompt based on intent and context
      const prompt = this.buildChatPrompt(userMessage, context, intent, candidateEntities);

      // Get response from Google Gemini
      const response = await this.callGeminiAPI(prompt);

      // Parse the chat response
      const chatResponse = this.parseChatResponse(response, intent, context, candidateEntities);

      // Add metadata
      chatResponse.metadata = {
        responseTime: Date.now() - startTime,
        llmProvider: 'GOOGLE_GEMINI',
      };

      this.logger.log(
        `Google Gemini chat response generated in ${chatResponse.metadata.responseTime}ms`,
      );

      return chatResponse;
    } catch (error) {
      this.logger.error('Error generating Google Gemini chat response', error.stack);
      return this.getFallbackChatResponse(userMessage, context);
    }
  }

  async classifyIntent(
    userMessage: string,
    context: ConversationContext,
  ): Promise<UserIntent> {
    this.logger.log(`Classifying intent for message: "${userMessage}"`);

    if (!this.apiKey) {
      return this.getFallbackIntent();
    }

    try {
      const prompt = this.buildIntentClassificationPrompt(userMessage, context);
      const response = await this.callGeminiAPI(prompt);
      return this.parseIntentResponse(response);
    } catch (error) {
      this.logger.error('Error classifying intent', error.stack);
      return this.getFallbackIntent();
    }
  }

  async generateFollowUpQuestions(
    context: ConversationContext,
  ): Promise<string[]> {
    this.logger.log(`Generating follow-up questions for session: ${context.sessionId}`);

    if (!this.apiKey) {
      return this.getFallbackFollowUpQuestions(context);
    }

    try {
      const prompt = this.buildFollowUpPrompt(context);
      const response = await this.callGeminiAPI(prompt);
      return this.parseFollowUpResponse(response);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error.stack);
      return this.getFallbackFollowUpQuestions(context);
    }
  }

  async shouldTransitionToRecommendations(
    context: ConversationContext,
  ): Promise<{ shouldTransition: boolean; reason: string }> {
    this.logger.log(`Evaluating transition for session: ${context.sessionId}`);

    if (!this.apiKey) {
      return { shouldTransition: false, reason: 'API key not available' };
    }

    try {
      const prompt = this.buildTransitionPrompt(context);
      const response = await this.callGeminiAPI(prompt);
      return this.parseTransitionResponse(response);
    } catch (error) {
      this.logger.error('Error evaluating transition', error.stack);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  // Private helper methods for chat functionality (reusing OpenAI patterns)
  private buildChatPrompt(
    userMessage: string,
    context: ConversationContext,
    intent: UserIntent,
    candidateEntities?: CandidateEntity[],
  ): string {
    // Use more conversation history for better context awareness
    const conversationHistory = context.messages
      .slice(-10) // Increased from 5 to 10 messages for better context
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    const entitiesContext = candidateEntities
      ? this.formatEntitiesForChat(candidateEntities)
      : '';

    const userProfile = this.formatUserProfile(context);

    // Extract topics and questions already discussed
    const discussedTopics = this.extractDiscussedTopics(context);
    const previousQuestions = this.extractPreviousQuestions(context);

    // Check if user is repeating the same question
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    const currentMessageLower = userMessage.toLowerCase();
    const isRepeatedQuestion = userMessages.some(msg =>
      msg.content.toLowerCase() === currentMessageLower &&
      msg.content !== userMessage // Not the current message
    );

    return `You are an expert AI assistant helping users discover the perfect AI tools for their needs. You are conversational, helpful, and knowledgeable about AI tools and their applications.

**CRITICAL: ANTI-REPETITION RULES**
- NEVER ask questions that have already been asked in this conversation
- NEVER repeat the same topics or suggestions you've already covered
- BUILD upon previous knowledge rather than starting over
- If the user has already provided information, acknowledge it and move forward
- Vary your language and approach even when covering similar ground
- If the user asks the same question again, acknowledge it and provide a DIFFERENT perspective or ask for clarification
- ALWAYS provide unique, varied responses even for similar questions

**Current Conversation Context:**
- Stage: ${context.conversationStage}
- User Intent: ${intent.type} (confidence: ${intent.confidence})
- Session: ${context.sessionId}
- Messages in conversation: ${context.messages?.length || 0}

**User Profile:**
${userProfile}

**Full Conversation History:**
${conversationHistory}

**Topics Already Discussed:**
${discussedTopics.length > 0 ? discussedTopics.join(', ') : 'None yet'}

**Questions Already Asked:**
${previousQuestions.length > 0 ? previousQuestions.join('\n- ') : 'None yet'}

**Current User Message:**
"${userMessage}"

${entitiesContext ? `**Relevant AI Tools to Consider:**\n${entitiesContext}` : ''}

**Instructions:**
1. Respond naturally and conversationally to the user's message
2. Based on the intent (${intent.type}), guide the conversation appropriately
3. If entities are provided, mention relevant ones naturally in your response
4. ONLY ask NEW questions that haven't been covered before
5. Build upon information already gathered rather than re-asking
6. Be encouraging and helpful throughout the conversation
7. If you've already asked about their work/industry/needs, don't ask again
8. Progress the conversation forward based on what you already know

**Response Format (JSON):**
{
  "message": "Your conversational response here",
  "discoveredEntities": [{"id": "entity-id", "name": "Tool Name", "relevanceScore": 0.9, "reason": "Why it's relevant"}],
  "followUpQuestions": ["Only NEW questions that haven't been asked before"],
  "suggestedActions": [{"type": "ask_question", "label": "Tell me more about...", "data": {}}],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "${context.conversationStage}"
}

Keep your message natural, helpful, and engaging. Focus on understanding their needs and guiding them toward the right AI tools WITHOUT repeating yourself.`;
  }

  private buildIntentClassificationPrompt(userMessage: string, context: ConversationContext): string {
    const recentMessages = context.messages
      .slice(-3)
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    return `Analyze the user's intent from their message and conversation context.

**Conversation Context:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Types:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high",
    "technical_level": "beginner|intermediate|advanced",
    "use_case": "specific use case if mentioned"
  }
}`;
  }

  private buildFollowUpPrompt(context: ConversationContext): string {
    const lastMessage = context.messages[context.messages.length - 1];

    return `Generate 2-3 intelligent follow-up questions to help the user discover the right AI tools.

**Conversation Stage:** ${context.conversationStage}
**User's Last Message:** "${lastMessage?.content || 'No previous message'}"
**Discovered Entities:** ${context.discoveredEntities.length} tools found so far

**Guidelines:**
- Ask questions that help narrow down their specific needs
- Consider their technical level and use case
- Be conversational and helpful
- Focus on practical aspects like budget, features, or use cases

**Response Format (JSON):**
{
  "questions": ["Question 1?", "Question 2?", "Question 3?"]
}`;
  }

  private buildTransitionPrompt(context: ConversationContext): string {
    return `Determine if the conversation is ready to transition to formal recommendations.

**Conversation Stage:** ${context.conversationStage}
**Messages Count:** ${context.messages.length}
**Discovered Entities:** ${context.discoveredEntities.length}
**User Preferences:** ${JSON.stringify(context.userPreferences)}

**Criteria for Transition:**
- User has clearly expressed their needs
- We have enough information about their requirements
- User seems ready for specific recommendations
- Conversation has progressed beyond initial discovery

**Response Format (JSON):**
{
  "shouldTransition": true/false,
  "reason": "Explanation of the decision"
}`;
  }

  // Parsing methods (similar to OpenAI implementation)
  private parseChatResponse(
    response: string,
    intent: UserIntent,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): ChatResponse {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // 🎯 RE-ENABLED: Entity validation with proper CandidateEntity objects
      const validatedDiscoveredEntities = this.validateDiscoveredEntities(
        parsed.discoveredEntities || [],
        candidateEntities || []
      );

      const baseResponse = {
        message: parsed.message || 'I\'m here to help you find the perfect AI tools!',
        intent,
        discoveredEntities: validatedDiscoveredEntities,
        followUpQuestions: parsed.followUpQuestions || [],
        suggestedActions: parsed.suggestedActions || [],
        shouldTransitionToRecommendations: parsed.shouldTransitionToRecommendations || false,
        conversationStage: parsed.conversationStage || context.conversationStage,
        metadata: {
          responseTime: 0,
          llmProvider: 'GOOGLE_GEMINI',
        },
      };

      // 🎯 TEMPORARILY DISABLED: Message content validation (causing repetition issues)
      // return this.validateMessageContentForHallucination(baseResponse, candidateEntities || []);
      return baseResponse;
    } catch (error) {
      this.logger.warn('Failed to parse Gemini chat response, using fallback', error.message);
      return this.getFallbackChatResponse('', context);
    }
  }

  /**
   * 🎯 CRITICAL: Validate discovered entities to prevent hallucination
   * Only allow entities that actually exist in our candidate entities list
   */
  private validateDiscoveredEntities(
    discoveredEntities: any[],
    candidateEntities: CandidateEntity[]
  ): Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }> {
    if (!discoveredEntities || !Array.isArray(discoveredEntities)) {
      return [];
    }

    const validEntityIds = new Set(candidateEntities.map(e => e.id));
    const validEntityNames = new Map(candidateEntities.map(e => [e.name.toLowerCase(), e]));

    const validatedEntities: Array<{
      id: string;
      name: string;
      relevanceScore: number;
      reason: string;
    }> = [];

    for (const entity of discoveredEntities) {
      let validEntity: CandidateEntity | null = null;

      // First, try to match by ID (most reliable)
      if (entity.id && validEntityIds.has(entity.id)) {
        validEntity = candidateEntities.find(e => e.id === entity.id) || null;
      }

      // If no ID match, try to match by name (case-insensitive)
      if (!validEntity && entity.name) {
        const nameLower = entity.name.toLowerCase();
        validEntity = validEntityNames.get(nameLower) || null;
      }

      // Only include if we found a valid match in our database
      if (validEntity) {
        validatedEntities.push({
          id: validEntity.id,
          name: validEntity.name, // Use the actual name from our database
          relevanceScore: Math.min(Math.max(entity.relevanceScore || 0.8, 0), 1), // Clamp between 0-1
          reason: entity.reason || `Relevant AI tool for your needs`
        });

        this.logger.debug(`Validated entity: ${validEntity.name} (${validEntity.id})`);
      } else {
        // Log hallucinated entities for monitoring
        this.logger.warn(`🚨 HALLUCINATED ENTITY DETECTED: ${JSON.stringify(entity)} - This entity does not exist in our database!`);
      }
    }

    this.logger.log(`Validated ${validatedEntities.length} out of ${discoveredEntities.length} discovered entities`);
    return validatedEntities;
  }

  /**
   * 🎯 CRITICAL: Validate message content for hallucinated tool mentions
   */
  private validateMessageContentForHallucination(
    response: ChatResponse,
    candidateEntities: CandidateEntity[]
  ): ChatResponse {
    if (!response.message) {
      return response;
    }

    const validToolNames = new Set(
      candidateEntities.map(entity => entity.name.toLowerCase())
    );

    const suspiciousPatterns = [
      /\b(ChatGPT|Claude|GPT-4|GPT-3|Midjourney|DALL-E|DALL·E|Stable Diffusion|Runway ML|Runway|Synthesia|Jasper|Copy\.ai|Writesonic|Grammarly|Notion AI|Canva|Figma|Adobe Firefly|Filmora|DaVinci Resolve|Luma|Pika|Descript|Replicate|Hugging Face|OpenAI|Anthropic|Cohere|AI21|Perplexity|Character\.ai|Bard|Bing Chat|GitHub Copilot|Codex|InstructGPT|PaLM|LaMDA|BERT|T5|RoBERTa)\b/gi,
      /\b([A-Z][a-z]{2,}(?:[A-Z][a-z]+)*)\s+(AI|Pro|Plus|Studio|Bot|Assistant|Generator|Creator|Maker|Builder)\b/g,
      /\b(Google|Microsoft|Meta|Facebook|Amazon|Apple|IBM|NVIDIA|Intel|Salesforce|Oracle)\s+(AI|Bard|Copilot|Assistant|Bot)\b/gi,
    ];

    let messageText = response.message;
    let foundHallucinations = false;

    for (const pattern of suspiciousPatterns) {
      const matches = messageText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const toolName = match.toLowerCase().trim();

          const commonWords = ['ai', 'pro', 'plus', 'studio', 'the', 'and', 'or', 'for', 'with', 'to', 'in', 'on', 'at', 'by'];
          if (commonWords.includes(toolName) || toolName.length < 3) {
            continue;
          }

          if (!validToolNames.has(toolName) && !this.isGenericTerm(toolName)) {
            this.logger.warn(`🚨 HALLUCINATED TOOL IN MESSAGE: "${match}" - This tool is not in our database!`);
            foundHallucinations = true;

            const genericReplacement = this.getGenericReplacement(match);
            messageText = messageText.replace(new RegExp(match, 'gi'), genericReplacement);
          }
        }
      }
    }

    if (foundHallucinations) {
      this.logger.warn(`🎯 MESSAGE CONTENT SANITIZED: Removed hallucinated tool mentions`);
      messageText += '\n\n*Note: I can only recommend AI tools that are verified and available in our database.*';

      return {
        ...response,
        message: messageText,
      };
    }

    return response;
  }

  private isGenericTerm(term: string): boolean {
    const genericTerms = [
      'ai', 'tool', 'software', 'platform', 'service', 'app', 'application',
      'system', 'solution', 'technology', 'program', 'website', 'online',
      'digital', 'virtual', 'smart', 'intelligent', 'automated', 'machine',
      'learning', 'neural', 'network', 'algorithm', 'model', 'api'
    ];
    return genericTerms.includes(term.toLowerCase());
  }

  private getGenericReplacement(toolName: string): string {
    if (toolName.toLowerCase().includes('ai')) {
      return 'AI tools';
    }
    if (toolName.toLowerCase().includes('video') || toolName.toLowerCase().includes('edit')) {
      return 'video editing tools';
    }
    if (toolName.toLowerCase().includes('write') || toolName.toLowerCase().includes('text')) {
      return 'writing tools';
    }
    if (toolName.toLowerCase().includes('image') || toolName.toLowerCase().includes('photo')) {
      return 'image generation tools';
    }
    return 'AI tools';
  }

  private parseIntentResponse(response: string): UserIntent {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        type: parsed.type || 'discovery',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        categories: parsed.categories || [],
        features: parsed.features || [],
        constraints: parsed.constraints || {},
      };
    } catch (error) {
      this.logger.warn('Failed to parse intent response, using fallback', error.message);
      return this.getFallbackIntent();
    }
  }

  private parseFollowUpResponse(response: string): string[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed.questions || [];
    } catch (error) {
      this.logger.warn('Failed to parse follow-up response, using fallback', error.message);
      return [];
    }
  }

  private parseTransitionResponse(response: string): { shouldTransition: boolean; reason: string } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return {
        shouldTransition: parsed.shouldTransition || false,
        reason: parsed.reason || 'Automatic evaluation',
      };
    } catch (error) {
      this.logger.warn('Failed to parse transition response, using fallback', error.message);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  // Helper methods (reusing patterns from OpenAI)
  private formatEntitiesForChat(entities: CandidateEntity[]): string {
    return entities
      .slice(0, 5)
      .map((entity, index) => {
        const categories = entity.categories.map(c => c.category.name).join(', ');
        const features = entity.features.map(f => f.feature.name).join(', ');

        return `${index + 1}. **${entity.name}**
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories || 'General'}
   - Key Features: ${features || 'Various features'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}`;
      })
      .join('\n\n');
  }

  private formatUserProfile(context: ConversationContext): string {
    const prefs = context.userPreferences;
    return `- Technical Level: ${prefs.technical_level || 'Not specified'}
- Budget Preference: ${prefs.budget || 'Not specified'}
- Preferred Categories: ${prefs.preferred_categories?.join(', ') || 'None specified'}
- Conversation Stage: ${context.conversationStage}
- Tools Discovered: ${context.discoveredEntities.length}`;
  }

  /**
   * Extract topics that have been discussed in the conversation
   */
  private extractDiscussedTopics(context: ConversationContext): string[] {
    const topics = new Set<string>();
    const messages = context.messages || [];

    // Common topic keywords to look for
    const topicKeywords = {
      'work/industry': ['work', 'job', 'industry', 'business', 'company', 'profession', 'career'],
      'technical level': ['beginner', 'intermediate', 'advanced', 'expert', 'technical', 'experience'],
      'budget': ['budget', 'cost', 'price', 'expensive', 'cheap', 'free', 'paid'],
      'programming': ['code', 'coding', 'programming', 'development', 'developer', 'software'],
      'education': ['education', 'teaching', 'learning', 'student', 'school', 'university'],
      'content creation': ['content', 'writing', 'video', 'image', 'design', 'creative'],
      'automation': ['automation', 'automate', 'workflow', 'process', 'efficiency'],
      'data analysis': ['data', 'analysis', 'analytics', 'insights', 'reporting'],
    };

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          topics.add(topic);
        }
      });
    });

    return Array.from(topics);
  }

  /**
   * Extract questions that have been asked by the assistant
   */
  private extractPreviousQuestions(context: ConversationContext): string[] {
    const questions: string[] = [];
    const messages = context.messages || [];

    messages.forEach(msg => {
      if (msg.role === 'assistant' && msg.content.includes('?')) {
        // Extract questions from assistant messages
        const questionMatches = msg.content.match(/[^.!]*\?/g);
        if (questionMatches) {
          questions.push(...questionMatches.map(q => q.trim()));
        }
      }
    });

    return questions;
  }

  // Fallback methods
  private getFallbackChatResponse(userMessage: string, context: ConversationContext): ChatResponse {
    const fallbackMessages = {
      greeting: "Hello! I'm here to help you discover the perfect AI tools for your needs. What kind of tasks are you looking to accomplish?",
      discovery: "I'd love to help you find the right AI tools! Could you tell me more about what you're trying to achieve?",
      refinement: "Let me help you narrow down the options. What specific features or capabilities are most important to you?",
      recommendation: "Based on our conversation, I can help you find some great options. Would you like me to show you some specific recommendations?",
      comparison: "I can help you compare different AI tools. What specific aspects would you like me to focus on?"
    };

    return {
      message: fallbackMessages[context.conversationStage] || fallbackMessages.discovery,
      intent: this.getFallbackIntent(),
      followUpQuestions: this.getFallbackFollowUpQuestions(context),
      shouldTransitionToRecommendations: false,
      conversationStage: context.conversationStage,
      metadata: {
        responseTime: 0,
        llmProvider: 'GOOGLE_GEMINI_FALLBACK',
      },
    };
  }

  private getFallbackIntent(): UserIntent {
    return {
      type: 'discovery',
      confidence: 0.5,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    };
  }

  private getFallbackFollowUpQuestions(context: ConversationContext): string[] {
    const questionsByStage = {
      greeting: [
        "What type of work or projects are you working on?",
        "Are you looking for tools for business or personal use?"
      ],
      discovery: [
        "What's your experience level with AI tools?",
        "Do you have a budget in mind for AI tools?",
        "What's the main challenge you're trying to solve?"
      ],
      refinement: [
        "Would you prefer free tools or are you open to paid options?",
        "How technical do you want the tool to be?",
        "Do you need integration with other software?"
      ],
      recommendation: [
        "Would you like to see some specific recommendations?",
        "Should I focus on the most popular options or newer tools?"
      ],
      comparison: [
        "What criteria are most important for your decision?",
        "Would you like me to compare pricing or features?"
      ]
    };

    return questionsByStage[context.conversationStage] || questionsByStage.discovery;
  }
}
